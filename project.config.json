{"simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-39"}, "projectArchitecture": "multiPlatform", "setting": {"condition": true, "es6": false, "postcss": false, "compileWorklet": false, "minified": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "packOptions": {"ignore": [{"type": "folder", "value": "backend"}, {"type": "folder", "value": "server"}, {"type": "suffix", "value": ".md"}, {"type": "suffix", "value": ".txt"}], "include": []}, "appid": "wx317f974906d46cc9", "editorSetting": {}}