/**
 * 测试时间格式化修复
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => '[]',
  setStorageSync: () => {},
};

const timeFormat = require('./utils/timeFormat.js');

console.log('=== 时间格式化测试 ===');

// 测试各种时间格式
const testCases = [
  // 标准ISO格式（新格式）
  new Date().toISOString(),
  
  // 中文本地化格式（旧格式）
  '2024/1/15 下午3:30:45',
  '2024/1/15 上午9:15:30',
  '2024/12/25 下午11:59:59',
  '2024/12/25 上午12:00:00',
  
  // 其他可能的格式
  '2024-01-15 15:30:45',
  '2024/01/15 15:30:45',
  
  // 无效格式
  'invalid date',
  '',
  null,
  undefined
];

testCases.forEach((testCase, index) => {
  console.log(`\n测试案例 ${index + 1}: ${testCase}`);
  
  try {
    const relativeTime = timeFormat.formatRelativeTime(testCase);
    const fullTime = timeFormat.formatFullDateTime(testCase);
    
    console.log(`  相对时间: ${relativeTime}`);
    console.log(`  完整时间: ${fullTime}`);
  } catch (error) {
    console.log(`  错误: ${error.message}`);
  }
});

console.log('\n=== 测试完成 ===');
