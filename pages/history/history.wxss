@import "/styles/theme.wxss";

/* 为整个页面设置布局 */
page {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  padding: 30rpx;
  padding-top: 60rpx;
  box-sizing: border-box;
  background-color: var(--primary-light);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  overflow: hidden;
}

/* 搜索栏样式 */
.search-bar {
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 0 20rpx;
  height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  height: 100%;
}

.clear-search-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.clear-search-btn image {
  width: 24rpx;
  height: 24rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  position: relative;
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.clear-history-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 56rpx;
  height: 56rpx;
  background-color: rgba(244, 143, 177, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: background-color 0.2s;
}

.clear-history-icon:active {
  background-color: rgba(244, 143, 177, 0.2);
}

.clear-history-icon image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
}

/* 滚动区域 */
.history-scroll {
  flex: 1;
  height: auto;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 历史记录列表 */
.history-list {
  padding: 80rpx 20rpx 20rpx 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.history-item {
  padding: 24rpx;
  background-color: var(--card-background, white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-xs);
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  transition: transform 0.2s;
}

.history-item:active {
  transform: scale(0.98);
}

.item-header {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.file-name {
  font-size: var(--font-size-md);
  font-weight: bold;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: var(--font-size-xs);
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.encoding-tag {
  font-size: var(--font-size-xs);
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 4rpx 16rpx;
  border-radius: var(--border-radius-sm);
}

.date {
  font-size: var(--font-size-xs);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx 60rpx;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 辅助类 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}