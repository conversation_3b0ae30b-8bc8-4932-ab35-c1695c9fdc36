// pages/history/history.js
const app = getApp();

Page({
  data: {
    historyFiles: [],
    filteredHistoryFiles: [], // 过滤后的历史记录
    searchKeyword: '' // 搜索关键词
  },

  onLoad: function() {
    this.loadHistoryFiles();
  },
  
  onShow: function() {
    // 每次显示页面时刷新历史记录
    this.loadHistoryFiles();
  },

  loadHistoryFiles: function() {
    const storage = require('../../utils/storage.js');
    const timeFormat = require('../../utils/timeFormat.js');

    const historyFiles = storage.getHistory();

    const formattedFiles = historyFiles.map(file => {
      return {
        ...file,
        relativeTime: timeFormat.formatRelativeTime(file.date),
        fullTime: timeFormat.formatFullDateTime(file.date)
      };
    });

    this.setData({
      historyFiles: formattedFiles,
      filteredHistoryFiles: formattedFiles // 初始时显示所有记录
    });
  },

  // 搜索输入处理
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    this.filterHistoryFiles(keyword);
  },

  // 搜索确认
  onSearchConfirm: function(e) {
    const keyword = e.detail.value;
    this.filterHistoryFiles(keyword);
  },

  // 清空搜索
  onClearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.filterHistoryFiles('');
  },

  // 过滤历史记录
  filterHistoryFiles: function(keyword) {
    const { historyFiles } = this.data;

    if (!keyword.trim()) {
      // 如果搜索关键词为空，显示所有记录
      this.setData({
        filteredHistoryFiles: historyFiles
      });
      return;
    }

    // 根据文件名过滤
    const filtered = historyFiles.filter(file =>
      file.fileName.toLowerCase().includes(keyword.toLowerCase())
    );

    this.setData({
      filteredHistoryFiles: filtered
    });
  },

  onHistoryItemTap: function(e) {
    const item = e.currentTarget.dataset.item;

    wx.getFileInfo({
      filePath: item.filePath,
      success: () => {
        wx.navigateTo({
          url: `/pages/preview/preview?filePath=${encodeURIComponent(item.filePath)}&fileName=${encodeURIComponent(item.fileName)}&fromHistory=true`
        });
      },
      fail: () => {
        wx.showToast({
          title: '文件已不存在',
          icon: 'none'
        });

        const storage = require('../../utils/storage.js');
        storage.removeHistoryItem(item.id);
        this.loadHistoryFiles();
      }
    });
  },

  // 清空历史记录
  onClearHistory: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          const storage = require('../../utils/storage.js');
          storage.clearHistory();
          this.loadHistoryFiles();

          wx.showToast({
            title: '历史记录已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  onShareAppMessage: function() {
    return {
      title: 'TXT编码转换器 - 轻松解决乱码问题',
      path: '/pages/home/<USER>'
    };
  }
});