// pages/home/<USER>
const app = getApp();

Page({
  data: {
    recentFiles: []
  },

  onLoad: function() {
    this.loadRecentFiles();
  },

  onShow: function() {
    // 每次显示页面时刷新最近文件列表
    this.loadRecentFiles();
  },

  loadRecentFiles: function() {
    const storage = require('../../utils/storage.js');
    const timeFormat = require('../../utils/timeFormat.js');

    const recentFiles = storage.getHistory().slice(0, 3); // 只显示最近3个

    // 格式化时间显示
    const formattedFiles = recentFiles.map(file => {
      return {
        ...file,
        relativeTime: timeFormat.formatRelativeTime(file.date),
        fullTime: timeFormat.formatFullDateTime(file.date) // 保留完整时间用于可能的详情显示
      };
    });

    this.setData({
      recentFiles: formattedFiles
    });
  },

  onChooseFile: function() {
    const that = this;
    
    wx.showActionSheet({
      itemList: ['从聊天记录选择'],
      success(res) {
        if (res.tapIndex === 0) {
          // 尝试从聊天记录选择
          if (wx.chooseMessageFile) {
            wx.chooseMessageFile({
              count: 1,
              type: 'file',
              extension: ['txt'],
              success(res) {
                const tempFilePath = res.tempFiles[0].path;
                const fileName = res.tempFiles[0].name;
                that.openPreview(tempFilePath, fileName);
              },
              fail(err) {
                console.error('选择文件失败:', err);
                if (err.errMsg.indexOf('chooseMessageFile:fail') !== -1) {
                  wx.showModal({
                    title: '提示',
                    content: '当前环境不支持从聊天记录选择文件。请将TXT文件发送到聊天中，然后在手机上使用微信打开小程序。',
                    showCancel: false
                  });
                } else {
                  wx.showToast({
                    title: '选择文件失败',
                    icon: 'none'
                  });
                }
              }
            });
          } else {
            // 设备不支持chooseMessageFile
            wx.showModal({
              title: '提示',
              content: '您的设备不支持文件选择功能。请确保微信版本已更新至最新版本。',
              showCancel: false
            });
          }
        }
      }
    });
  },

  onHistoryTap: function() {
    wx.navigateTo({
      url: '/pages/history/history'
    });
  },

  onRecentTap: function(e) {
    const item = e.currentTarget.dataset.item;
    
    // 检查文件是否仍然存在
    wx.getFileSystemManager().getFileInfo({
      filePath: item.filePath,
      success: () => {
        wx.navigateTo({
          url: `/pages/preview/preview?filePath=${encodeURIComponent(item.filePath)}&fileName=${encodeURIComponent(item.fileName)}&fromHistory=true`
        });
      },
      fail: () => {
        wx.showToast({
          title: '文件已不存在',
          icon: 'none'
        });
        
        // 从历史记录中移除
        const storage = require('../../utils/storage.js');
        storage.removeHistoryItem(item.id);
        this.loadRecentFiles();
      }
    });
  },

  openPreview: function(filePath, fileName) {
    // 检查filePath是否有效
    if (!filePath) {
      wx.showToast({
        title: '文件路径无效',
        icon: 'none'
      });
      return;
    }
    
    const that = this;
    // 文件大小检查
    wx.getFileSystemManager().getFileInfo({
      filePath: filePath,
      success(res) {
        // 确保文件大小是有效的数字
        let fileSizeMB = 0;
        if (typeof res.size === 'number' && !isNaN(res.size)) {
          fileSizeMB = res.size / (1024 * 1024);
        }
        
        if (fileSizeMB > 2) {
          wx.showModal({
            title: '文件较大',
            content: '文件大小超过2MB，打开可能较慢，是否继续？',
            success(modalRes) {
              if (modalRes.confirm) {
                wx.navigateTo({
                  url: `/pages/preview/preview?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`
                });
              }
            }
          });
        } else {
          wx.navigateTo({
            url: `/pages/preview/preview?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`
          });
        }
      },
      fail(err) {
        console.error('获取文件信息失败:', err);
        // 尝试直接打开文件，而不是显示错误
        wx.navigateTo({
          url: `/pages/preview/preview?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`
        });
      }
    });
  }
}); 