<navigation-bar title="TXT转换器" showBack="{{false}}"></navigation-bar>

<view class="container">
  <view class="header">
    <image class="logo" src="/assets/images/logo.png" mode="aspectFit"></image>
    <view class="title">TXT编码转换器</view>
    <view class="subtitle">轻松解决乱码问题</view>
  </view>
  
  <view class="action-cards">
    <view class="card file-card" bindtap="onChooseFile">
      <image class="card-icon" src="/assets/images/file-icon.png" mode="aspectFit"></image>
      <view class="card-title">选择文件</view>
      <view class="card-desc">从微信聊天或本地选择TXT文件</view>
    </view>
    
    <view class="card history-card" bindtap="onHistoryTap">
      <image class="card-icon" src="/assets/images/history-icon.png" mode="aspectFit"></image>
      <view class="card-title">历史记录</view>
      <view class="card-desc">查看最近打开的文件</view>
    </view>
  </view>
  
  <view class="recent-files" wx:if="{{recentFiles.length > 0}}">
    <view class="section-title">最近文件</view>
    <view class="recent-list">
      <view class="recent-item card" wx:for="{{recentFiles}}" wx:key="id" bindtap="onRecentTap" data-item="{{item}}">
        <view class="recent-name text-primary">{{item.fileName}}</view>
        <view class="recent-info text-secondary">
          <text>{{item.encoding}}</text>
          <text>{{item.relativeTime}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 