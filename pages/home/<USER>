@import "/styles/theme.wxss";

.container {
  padding: 30rpx;
  padding-top: 80rpx;
  min-height: 100vh;
  background-color: var(--primary-light);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  margin-top: 30rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.action-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  margin-top: 20rpx;
  align-self: center;
  width: 100%;
}

.card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: 30rpx;
  width: 48%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s;
}

.card:active {
  transform: scale(0.98);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: var(--font-size-md);
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.card-desc {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  text-align: center;
}

.file-card {
  border-top: 8rpx solid var(--primary-color);
}

.history-card {
  border-top: 8rpx solid var(--accent-color);
}

/* 最近文件区域 */
.recent-files {
  width: 100%;
  margin-top: 20rpx;
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
}

.recent-item {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.recent-name {
  font-size: var(--font-size-md);
  margin-bottom: 10rpx;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-info {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  width: 100%;
} 