from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import chardet
import base64
from typing import Optional, Tuple
import os
import psutil
from functools import lru_cache

# 获取系统信息，用于自动调整配置
TOTAL_MEMORY = psutil.virtual_memory().total / (1024 * 1024 * 1024)  # GB
CPU_COUNT = os.cpu_count() or 2
# 为2核2G环境优化的配置
WORKERS = min(CPU_COUNT, 2)  # 最多使用2个worker
MAX_CACHE_SIZE = int(TOTAL_MEMORY * 100)  # 根据内存大小调整缓存条目数
MAX_SAMPLE_SIZE = 4096  # 编码检测的最大样本大小

app = FastAPI(
    title="TXT编码转换API",
    version="1.0.0",
    docs_url="/docs",  # 保留文档，但在生产环境可考虑设为None
    redoc_url=None     # 禁用redoc以节省资源
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class DetectRequest(BaseModel):
    file_data: str  # base64编码的文件数据

class ReadRequest(BaseModel):
    file_data: str  # base64编码的文件数据
    target_encoding: Optional[str] = None  # 可选的目标编码

# 优化的编码检测函数，只检测文件的一部分
@lru_cache(maxsize=MAX_CACHE_SIZE)
def cached_detect_encoding(file_bytes_hash: str, sample: bytes) -> Tuple[str, float]:
    result = chardet.detect(sample)
    return (result.get("encoding", "utf-8"), result.get("confidence", 0))

@app.post("/api/detect-encoding")
async def detect_encoding(request: DetectRequest):
    """检测文件编码"""
    try:
        file_bytes = base64.b64decode(request.file_data)
        # 只取前4KB进行编码检测，减少内存使用
        sample = file_bytes[:MAX_SAMPLE_SIZE]
        file_hash = str(hash(sample))

        # 使用缓存检测编码
        encoding, confidence = cached_detect_encoding(file_hash, sample)

        return {
            "encoding": encoding,
            "confidence": confidence
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"编码检测失败: {str(e)}")

@app.post("/api/read-file")
async def read_file(request: ReadRequest):
    """读取文件并可选地转换编码"""
    try:
        file_bytes = base64.b64decode(request.file_data)

        # 只取前4KB进行编码检测
        sample = file_bytes[:MAX_SAMPLE_SIZE]
        file_hash = str(hash(sample))

        # 使用缓存检测编码
        source_encoding, confidence = cached_detect_encoding(file_hash, sample)

        # 使用检测到的编码解码文件
        try:
            text = file_bytes.decode(source_encoding, errors='ignore')
        except (UnicodeDecodeError, LookupError):
            # 如果检测到的编码失败，尝试使用UTF-8
            text = file_bytes.decode('utf-8', errors='ignore')
            source_encoding = 'utf-8'

        # 如果指定了目标编码且与源编码不同，则转换
        if request.target_encoding and request.target_encoding != source_encoding:
            try:
                # 重新编码为目标编码，然后解码回来以模拟转换效果
                converted_bytes = text.encode(request.target_encoding, errors='ignore')
                converted_text = converted_bytes.decode(request.target_encoding, errors='ignore')

                return {
                    "original_text": text,
                    "converted_text": converted_text,
                    "source_encoding": source_encoding,
                    "target_encoding": request.target_encoding,
                    "confidence": confidence
                }
            except (UnicodeEncodeError, LookupError) as e:
                raise HTTPException(status_code=400, detail=f"编码转换失败: {str(e)}")

        # 没有指定目标编码，只返回原始文本
        return {
            "original_text": text,
            "source_encoding": source_encoding,
            "confidence": confidence
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件读取失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, workers=WORKERS)