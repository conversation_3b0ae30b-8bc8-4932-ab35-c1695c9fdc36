# TXT编码转换后端API

## 本地运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 启动服务：
```bash
python main.py
```

3. 访问API文档：http://localhost:8000/docs

## 部署到云服务

### 使用Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 环境变量配置

在小程序中修改 `utils/api.js` 的 `API_BASE_URL` 为实际部署地址。