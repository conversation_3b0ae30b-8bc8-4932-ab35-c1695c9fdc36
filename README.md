# TXT编码转换器小程序

一个微信小程序，用于打开TXT文件并进行编码格式转换，解决乱码问题。

## 功能特点

- 支持从微信聊天记录或本地选择TXT文件
- 自动检测文件编码格式
- 支持多种编码格式的转换（UTF-8, GBK, GB18030, Big5等）
- 可保存转换后的文件或分享给好友
- 历史记录功能，方便重复访问
- 可爱美观的UI设计

## 支持的编码格式

- UTF-8
- GBK
- GB18030
- Big5
- Shift_JIS
- EUC-JP
- EUC-KR
- ISO-8859-1
- Windows-1252

## 项目结构

```
├── pages/                 # 页面目录
│   ├── home/              # 首页（文件选择入口）
│   ├── preview/           # 文件预览与编码转换页
│   ├── history/           # 历史记录页
│   └── index/             # 原始索引页（已弃用）
├── components/            # 组件目录
│   ├── navigation-bar/    # 导航栏组件
│   └── encoding-selector/ # 编码选择器组件
├── utils/                 # 工具函数
│   ├── encoding.js        # 编码检测与转换工具
│   └── storage.js         # 历史记录存储工具
├── styles/                # 全局样式
│   └── theme.wxss         # 主题样式变量
├── assets/                # 静态资源（图片等）
├── miniprogram_npm/       # npm包构建目录
└── app.js, app.json, app.wxss  # 应用程序配置文件
```

## 开发环境配置

### 前提条件

- 微信开发者工具
- Node.js 和 npm

### 安装依赖

1. 克隆项目到本地
2. 在项目根目录执行以下命令安装依赖：

```bash
npm install jschardet@3.0.0 --save
```

3. 在微信开发者工具中，点击"工具" -> "构建npm"

### 开发调试

1. 使用微信开发者工具打开项目
2. 在模拟器中预览或使用真机调试

## 编码检测原理

本项目使用 jschardet 库进行编码检测。该库是 Mozilla 的 Universal Charset Detector 的 JavaScript 移植版本，可以检测多种编码格式。

检测流程：
1. 读取文件的二进制数据
2. 使用 jschardet 分析数据，获取可能的编码及置信度
3. 根据检测结果，尝试以该编码解析文件内容
4. 如果置信度较低或解析失败，提示用户手动选择编码

## 测试

### 单元测试

使用 Jest 进行单元测试：

```bash
npm test
```

### 手动测试

1. 准备多种编码的TXT文件（UTF-8, GBK, GB18030等）
2. 在小程序中打开这些文件，验证编码检测是否准确
3. 尝试转换编码并保存，检查结果是否正确

## 注意事项

- 文件大小限制：建议文件大小不超过2MB，过大的文件可能导致处理缓慢
- 编码检测置信度：某些情况下编码检测可能不够准确，此时需要用户手动选择正确的编码
- 临时文件：转换后的文件保存在小程序的临时目录中，重启小程序后可能无法访问

## 许可证

[MIT](LICENSE) 