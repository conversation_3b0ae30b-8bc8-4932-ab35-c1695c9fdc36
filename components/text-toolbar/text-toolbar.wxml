<view class="text-toolbar {{show ? 'show' : 'hidden'}}">
  <view class="toolbar-main">
    <!-- 主工具栏 -->
    <view class="toolbar-item" bindtap="toggleTheme">
      <view class="toolbar-icon {{themeMode === 'dark' ? 'icon-light' : 'icon-dark'}}">
        {{themeMode === 'dark' ? '☀️' : '🌙'}}
      </view>
      <view class="toolbar-text">{{themeMode === 'dark' ? '亮色' : '暗色'}}</view>
    </view>
    
    <view class="toolbar-item" bindtap="showFontSettings">
      <view class="toolbar-icon icon-font">Aa</view>
      <view class="toolbar-text">字体</view>
    </view>
    
    <view class="toolbar-item" bindtap="toggleWordWrap">
      <view class="toolbar-icon {{wordWrap ? 'icon-wrap-on' : 'icon-wrap-off'}}">
        {{wordWrap ? '↩️' : '⇢'}}
      </view>
      <view class="toolbar-text">{{wordWrap ? '换行' : '不换行'}}</view>
    </view>
    
    <view class="toolbar-item" bindtap="copyContent">
      <view class="toolbar-icon icon-copy">📋</view>
      <view class="toolbar-text">复制</view>
    </view>
    
    <view class="toolbar-item" bindtap="toggleEditMode">
      <view class="toolbar-icon {{editMode ? 'icon-view' : 'icon-edit'}}">
        {{editMode ? '👁️' : '✏️'}}
      </view>
      <view class="toolbar-text">{{editMode ? '查看' : '编辑'}}</view>
    </view>
    
    <view class="toolbar-item" bindtap="shareContent">
      <view class="toolbar-icon icon-share">📤</view>
      <view class="toolbar-text">分享</view>
    </view>
  </view>
</view>

<!-- 字体设置面板背景遮罩 -->
<view class="font-panel-mask {{showFontPanel ? 'show' : ''}}" bindtap="hideFontSettings"></view>

<!-- 字体设置面板 -->
<view class="font-panel {{showFontPanel ? 'show' : ''}}">
  <view class="panel-header">
    <view class="panel-title">字体设置</view>
    <view class="panel-close" bindtap="hideFontSettings">×</view>
  </view>
  
  <view class="setting-item">
    <view class="setting-label">字体大小</view>
    <view class="setting-control">
      <view class="control-btn" bindtap="decreaseFontSize">-</view>
      <view class="control-value">{{fontSize}}px</view>
      <view class="control-btn" bindtap="increaseFontSize">+</view>
    </view>
  </view>
  
  <view class="setting-item">
    <view class="setting-label">行间距</view>
    <view class="setting-control">
      <view class="control-btn" bindtap="decreaseLineHeight">-</view>
      <view class="control-value">{{lineHeight}}</view>
      <view class="control-btn" bindtap="increaseLineHeight">+</view>
    </view>
  </view>
</view> 