// components/text-toolbar/text-toolbar.js
Component({
  properties: {
    // 是否启用编辑模式
    editMode: {
      type: Boolean,
      value: false
    },
    // 是否显示工具栏
    show: {
      type: Boolean,
      value: true
    },
    // 当前主题模式 (light/dark)
    themeMode: {
      type: String,
      value: 'light'
    },
    // 当前字体大小
    fontSize: {
      type: Number,
      value: 14
    },
    // 当前行间距
    lineHeight: {
      type: Number,
      value: 1.6
    },
    // 是否自动换行
    wordWrap: {
      type: Boolean,
      value: true
    }
  },

  data: {
    showFontPanel: false
  },

  methods: {
    // 切换主题模式
    toggleTheme: function() {
      const newMode = this.properties.themeMode === 'light' ? 'dark' : 'light';
      this.triggerEvent('themeModeChange', { mode: newMode });
    },
    
    // 切换编辑模式
    toggleEditMode: function() {
      this.triggerEvent('editModeChange', { mode: !this.properties.editMode });
    },
    
    // 显示字体面板
    showFontSettings: function() {
      this.setData({
        showFontPanel: true
      });
      
      // 防止滚动穿透
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
    },
    
    // 隐藏字体面板
    hideFontSettings: function() {
      this.setData({
        showFontPanel: false
      });
    },
    
    // 字体增大
    increaseFontSize: function() {
      if (this.properties.fontSize >= 24) return;
      this.triggerEvent('fontSizeChange', { fontSize: this.properties.fontSize + 2 });
    },
    
    // 字体减小
    decreaseFontSize: function() {
      if (this.properties.fontSize <= 10) return;
      this.triggerEvent('fontSizeChange', { fontSize: this.properties.fontSize - 2 });
    },
    
    // 增加行间距
    increaseLineHeight: function() {
      if (this.properties.lineHeight >= 2.4) return;
      this.triggerEvent('lineHeightChange', { lineHeight: parseFloat((this.properties.lineHeight + 0.2).toFixed(1)) });
    },
    
    // 减少行间距
    decreaseLineHeight: function() {
      if (this.properties.lineHeight <= 1.0) return;
      this.triggerEvent('lineHeightChange', { lineHeight: parseFloat((this.properties.lineHeight - 0.2).toFixed(1)) });
    },
    
    // 切换自动换行
    toggleWordWrap: function() {
      this.triggerEvent('wordWrapChange', { wordWrap: !this.properties.wordWrap });
    },
    
    // 复制全文
    copyContent: function() {
      this.triggerEvent('copyContent');
    },
    
    // 分享文本
    shareContent: function() {
      this.triggerEvent('shareContent');
    }
  }
}); 