/**
 * 编码检测与转换工具 - 后端API版本
 */

const api = require('./api.js');

/**
 * 检测文本文件的编码（调用后端API）
 * @param {ArrayBuffer} fileData - 文件的二进制数据
 * @returns {Promise<Object>} 包含编码名称和置信度的对象
 */
function detectEncoding(fileData) {
  return api.detectEncodingAPI(fileData);
}

/**
 * 读取文件并可选地转换编码
 * @param {ArrayBuffer} fileData - 文件的二进制数据
 * @param {string} targetEncoding - 可选的目标编码
 * @returns {Promise<Object>} 包含原始文本和转换后文本的对象
 */
function readFileWithEncoding(fileData, targetEncoding = null) {
  return api.readFileAPI(fileData, targetEncoding);
}

/**
 * 获取支持的编码列表
 * @returns {Array<string>} 支持的编码列表
 */
function getSupportedEncodings() {
  return [
    'UTF-8', 'GBK', 'GB18030', 'Big5',
    'Shift_JIS', 'EUC-JP', 'EUC-KR',
    'ISO-8859-1', 'Windows-1252'
  ];
}

module.exports = {
  detectEncoding,
  readFileWithEncoding,
  getSupportedEncodings
};