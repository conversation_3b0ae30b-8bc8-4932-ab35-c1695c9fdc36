// API配置 - 请修改为您的实际后端地址
const API_BASE_URL = 'https://txt.ballplus.asia'; 

/**
 * 将ArrayBuffer转换为base64字符串
 */
function arrayBufferToBase64(buffer) {
  return wx.arrayBufferToBase64(buffer);
}

/**
 * 调用后端检测编码
 */
function detectEncodingAPI(fileData) {
  return new Promise((resolve, reject) => {
    const base64Data = arrayBufferToBase64(fileData);
    
    wx.request({
      url: `${API_BASE_URL}/api/detect-encoding`,
      method: 'POST',
      header: {
        'content-type': 'application/json'
      },
      data: {
        file_data: base64Data
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`API调用失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(new Error(`网络请求失败: ${err.errMsg}`));
      }
    });
  });
}

/**
 * 读取文件并可选地转换编码
 * @param {ArrayBuffer} fileData - 文件的二进制数据
 * @param {string} targetEncoding - 可选的目标编码
 */
function readFileAPI(fileData, targetEncoding = null) {
  return new Promise((resolve, reject) => {
    const base64Data = arrayBufferToBase64(fileData);

    const requestData = {
      file_data: base64Data
    };

    if (targetEncoding) {
      requestData.target_encoding = targetEncoding;
    }

    wx.request({
      url: `${API_BASE_URL}/api/read-file`,
      method: 'POST',
      header: {
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`API调用失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(new Error(`网络请求失败: ${err.errMsg}`));
      }
    });
  });
}

module.exports = {
  detectEncodingAPI,
  readFileAPI
};