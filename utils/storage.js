/**
 * 历史记录存储工具
 * 使用微信小程序的Storage API进行本地存储
 */

// 历史记录的存储键名
const HISTORY_STORAGE_KEY = 'txt_converter_history';

// 最大历史记录数量
const MAX_HISTORY_COUNT = 50;

/**
 * 获取所有历史记录
 * @returns {Array} 历史记录数组，按时间倒序排列
 */
function getHistory() {
  try {
    const historyData = wx.getStorageSync(HISTORY_STORAGE_KEY);
    return historyData ? JSON.parse(historyData) : [];
  } catch (error) {
    console.error('读取历史记录失败:', error);
    return [];
  }
}

/**
 * 添加历史记录
 * @param {Object} record - 历史记录对象
 * @param {string} record.fileName - 文件名
 * @param {string} record.filePath - 文件路径
 * @param {string} record.encoding - 文件编码
 * @param {string} record.date - 日期字符串
 * @param {string} record.fileSize - 文件大小字符串
 * @returns {boolean} 是否添加成功
 */
function addHistory(record) {
  try {
    if (!record || !record.fileName || !record.filePath) {
      return false;
    }
    
    // 生成唯一ID
    record.id = generateId();
    
    // 获取现有历史记录
    let history = getHistory();
    
    // 检查是否已存在相同文件路径的记录，如果有则更新
    const existingIndex = history.findIndex(item => item.filePath === record.filePath);
    if (existingIndex !== -1) {
      history.splice(existingIndex, 1);
    }
    
    // 添加新记录到开头
    history.unshift(record);
    
    // 限制历史记录数量
    if (history.length > MAX_HISTORY_COUNT) {
      history = history.slice(0, MAX_HISTORY_COUNT);
    }
    
    // 保存更新后的历史记录
    wx.setStorageSync(HISTORY_STORAGE_KEY, JSON.stringify(history));
    return true;
  } catch (error) {
    console.error('添加历史记录失败:', error);
    return false;
  }
}

/**
 * 移除单个历史记录
 * @param {string} id - 记录ID
 * @returns {boolean} 是否移除成功
 */
function removeHistoryItem(id) {
  try {
    if (!id) return false;
    
    // 获取现有历史记录
    let history = getHistory();
    
    // 移除指定ID的记录
    const newHistory = history.filter(item => item.id !== id);
    
    // 如果没有变化，说明没找到记录
    if (newHistory.length === history.length) {
      return false;
    }
    
    // 保存更新后的历史记录
    wx.setStorageSync(HISTORY_STORAGE_KEY, JSON.stringify(newHistory));
    return true;
  } catch (error) {
    console.error('移除历史记录失败:', error);
    return false;
  }
}

/**
 * 清空所有历史记录
 * @returns {boolean} 是否清空成功
 */
function clearHistory() {
  try {
    wx.setStorageSync(HISTORY_STORAGE_KEY, JSON.stringify([]));
    return true;
  } catch (error) {
    console.error('清空历史记录失败:', error);
    return false;
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return 'id_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}

module.exports = {
  getHistory,
  addHistory,
  removeHistoryItem,
  clearHistory
}; 