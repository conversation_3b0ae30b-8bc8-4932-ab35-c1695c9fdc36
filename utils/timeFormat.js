/**
 * 时间格式化工具
 */

/**
 * 格式化相对时间
 * @param {string|Date} dateTime - 时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
function formatRelativeTime(dateTime) {
  try {
    const now = new Date();
    let targetTime = new Date(dateTime);

    // 检查时间是否有效，如果无效尝试其他解析方式
    if (isNaN(targetTime.getTime())) {
      targetTime = parseDateTime(dateTime);
      if (isNaN(targetTime.getTime())) {
        return '时间无效';
      }
    }
    
    const diffMs = now.getTime() - targetTime.getTime();
    
    // 如果是未来时间，直接显示具体时间
    if (diffMs < 0) {
      return formatDateTime(targetTime);
    }
    
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    // 1分钟内
    if (diffMinutes < 1) {
      return '刚刚';
    }
    
    // 1小时内
    if (diffHours < 1) {
      return `${diffMinutes}分钟前`;
    }
    
    // 1天内
    if (diffDays < 1) {
      return `${diffHours}小时前`;
    }
    
    // 1周内
    if (diffDays < 7) {
      return `${diffDays}天前`;
    }
    
    // 超过1周，显示具体日期
    return formatDateTime(targetTime);
    
  } catch (error) {
    console.error('时间格式化失败:', error);
    return '时间格式错误';
  }
}

/**
 * 格式化具体时间（年月日）
 * @param {Date} date - Date对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateTime(date) {
  try {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    const currentYear = new Date().getFullYear();
    
    // 如果是当年，不显示年份
    if (year === currentYear) {
      return `${month}-${day}`;
    }
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return '日期格式错误';
  }
}

/**
 * 格式化完整时间（包含时分秒）
 * @param {string|Date} dateTime - 时间字符串或Date对象
 * @returns {string} 格式化后的完整时间字符串
 */
function formatFullDateTime(dateTime) {
  try {
    const date = new Date(dateTime);
    
    if (isNaN(date.getTime())) {
      return '时间无效';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('完整时间格式化失败:', error);
    return '时间格式错误';
  }
}

module.exports = {
  formatRelativeTime,
  formatDateTime,
  formatFullDateTime
};